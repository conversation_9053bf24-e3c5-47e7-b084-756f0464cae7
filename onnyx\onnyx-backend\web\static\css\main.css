/* ===== ONNYX PRECISION DESIGN SYSTEM V3.0 ===== */
/* Modular, Consistent, Performance-Optimized Cyberpunk Design System */

:root {
    /* ===== MATHEMATICAL 8PX GRID FOUNDATION ===== */
    --base-unit: 8px;              /* Foundation unit - all measurements derive from this */
    --base-2: 16px;                /* 2 * base-unit */
    --base-3: 24px;                /* 3 * base-unit */
    --base-4: 32px;                /* 4 * base-unit */
    --base-5: 40px;                /* 5 * base-unit */
    --base-6: 48px;                /* 6 * base-unit */
    --base-8: 64px;                /* 8 * base-unit */
    --base-10: 80px;               /* 10 * base-unit */
    --base-12: 96px;               /* 12 * base-unit */
    --base-16: 128px;              /* 16 * base-unit */
    --base-20: 160px;              /* 20 * base-unit */
    --base-24: 192px;              /* 24 * base-unit */

    /* ===== CORE ONYX PALETTE ===== */
    --onyx-black: #1a1a1a;         /* Primary background */
    --onyx-gray: #2a2a2a;          /* Secondary background */
    --onyx-light: #3a3a3a;         /* Tertiary background */
    --onyx-lighter: #4a4a4a;       /* Quaternary background */
    --onyx-accent: #5a5a5a;        /* Accent background */

    /* ===== CYBERPUNK ACCENT COLORS ===== */
    --cyber-cyan: #00fff7;         /* Primary accent - tribal cyan */
    --cyber-cyan-bright: #33fffa;  /* Bright variant */
    --cyber-cyan-dark: #00cccc;    /* Dark variant */
    --cyber-cyan-glow: rgba(0, 255, 247, 0.3); /* Glow effect */

    --cyber-purple: #9a00ff;       /* Secondary accent - tribal purple */
    --cyber-purple-bright: #b333ff; /* Bright variant */
    --cyber-purple-dark: #7700cc;  /* Dark variant */
    --cyber-purple-glow: rgba(154, 0, 255, 0.3); /* Glow effect */

    --cyber-blue: #0080ff;         /* Tertiary accent - tribal blue */
    --cyber-blue-bright: #3399ff;  /* Bright variant */
    --cyber-blue-dark: #0066cc;    /* Dark variant */
    --cyber-blue-glow: rgba(0, 128, 255, 0.3); /* Glow effect */

    --cyber-green: #00ff88;        /* Success color */
    --cyber-green-bright: #33ffaa; /* Bright variant */
    --cyber-green-dark: #00cc66;   /* Dark variant */
    --cyber-green-glow: rgba(0, 255, 136, 0.3); /* Glow effect */

    --cyber-red: #ff3366;          /* Error color */
    --cyber-yellow: #ffcc00;       /* Warning color */
    --cyber-orange: #ff6600;       /* Info color */

    /* ===== SEMANTIC COLOR MAPPING ===== */
    --color-primary: var(--cyber-cyan);
    --color-secondary: var(--cyber-purple);
    --color-accent: var(--cyber-blue);
    --color-success: var(--cyber-green);
    --color-warning: var(--cyber-yellow);
    --color-error: var(--cyber-red);
    --color-info: var(--cyber-orange);

    /* ===== UNIFIED GLASS MORPHISM SYSTEM ===== */
    --glass-bg-light: rgba(255, 255, 255, 0.05);   /* Light glass background */
    --glass-bg: rgba(255, 255, 255, 0.08);         /* Standard glass background */
    --glass-bg-medium: rgba(255, 255, 255, 0.12);  /* Medium glass background */
    --glass-bg-heavy: rgba(255, 255, 255, 0.16);   /* Heavy glass background */

    --glass-border-light: rgba(255, 255, 255, 0.08); /* Light glass border */
    --glass-border: rgba(255, 255, 255, 0.16);     /* Standard glass border */
    --glass-border-heavy: rgba(255, 255, 255, 0.24); /* Heavy glass border */

    --glass-hover: rgba(255, 255, 255, 0.15);      /* Hover state background */
    --glass-active: rgba(255, 255, 255, 0.20);     /* Active state background */

    --glass-blur: 16px;            /* Standard blur amount */
    --glass-blur-light: 12px;      /* Light blur amount */
    --glass-blur-heavy: 20px;      /* Heavy blur amount */

    /* ===== TYPOGRAPHY SYSTEM ===== */
    --text-primary: #ffffff;       /* Primary text - pure white */
    --text-secondary: #f0f0f0;     /* Secondary text - light gray */
    --text-tertiary: #cccccc;      /* Tertiary text - medium gray */
    --text-muted: #999999;         /* Muted text - dark gray */
    --text-disabled: #666666;      /* Disabled text - darker gray */

    /* ===== MATHEMATICAL TYPOGRAPHY SCALE ===== */
    /* Based on 1.25 ratio (Major Third) for harmonic progression */
    --font-size-xs: 0.75rem;       /* 12px */
    --font-size-sm: 0.875rem;      /* 14px */
    --font-size-base: 1rem;        /* 16px */
    --font-size-lg: 1.125rem;      /* 18px */
    --font-size-xl: 1.25rem;       /* 20px */
    --font-size-2xl: 1.5rem;       /* 24px */
    --font-size-3xl: 1.875rem;     /* 30px */
    --font-size-4xl: 2.25rem;      /* 36px */
    --font-size-5xl: 3rem;         /* 48px */
    --font-size-6xl: 3.75rem;      /* 60px */

    /* ===== CONSISTENT SPACING SCALE ===== */
    /* All values are multiples of 8px for perfect grid alignment */
    --space-0: 0;                  /* 0px */
    --space-1: var(--base-unit);   /* 8px */
    --space-2: var(--base-2);      /* 16px */
    --space-3: var(--base-3);      /* 24px */
    --space-4: var(--base-4);      /* 32px */
    --space-5: var(--base-5);      /* 40px */
    --space-6: var(--base-6);      /* 48px */
    --space-8: var(--base-8);      /* 64px */
    --space-10: var(--base-10);    /* 80px */
    --space-12: var(--base-12);    /* 96px */
    --space-16: var(--base-16);    /* 128px */
    --space-20: var(--base-20);    /* 160px */
    --space-24: var(--base-24);    /* 192px */

    /* ===== BORDER RADIUS SCALE ===== */
    --radius-none: 0;              /* Sharp corners */
    --radius-sm: 4px;              /* Subtle rounding */
    --radius-md: var(--base-unit); /* Standard rounding (8px) */
    --radius-lg: 12px;             /* Pronounced rounding */
    --radius-xl: var(--base-2);    /* Large rounding (16px) */
    --radius-2xl: var(--base-3);   /* Extra large rounding (24px) */
    --radius-3xl: var(--base-4);   /* Maximum rounding (32px) */
    --radius-full: 9999px;         /* Full circle/pill shape */

    /* ===== SHADOW SYSTEM ===== */
    --shadow-xs: 0 1px 2px rgba(0, 0, 0, 0.05);
    --shadow-sm: 0 2px 4px rgba(0, 0, 0, 0.1);
    --shadow-md: 0 4px 8px rgba(0, 0, 0, 0.15);
    --shadow-lg: 0 8px 16px rgba(0, 0, 0, 0.2);
    --shadow-xl: 0 16px 32px rgba(0, 0, 0, 0.25);
    --shadow-2xl: 0 24px 48px rgba(0, 0, 0, 0.3);
    --shadow-glow-cyan: 0 0 var(--base-3) var(--cyber-cyan-glow);
    --shadow-glow-purple: 0 0 var(--base-3) var(--cyber-purple-glow);
    --shadow-glow-blue: 0 0 var(--base-3) var(--cyber-blue-glow);

    /* ===== ANIMATION TIMING ===== */
    --transition-instant: 100ms ease-out;
    --transition-fast: 200ms ease-out;
    --transition-normal: 300ms ease-out;
    --transition-slow: 500ms ease-out;
    --transition-slower: 700ms ease-out;

    /* ===== LAYOUT CONTAINERS ===== */
    --container-xs: 480px;         /* Extra small container */
    --container-sm: 640px;         /* Small container */
    --container-md: 768px;         /* Medium container */
    --container-lg: 1024px;        /* Large container */
    --container-xl: 1280px;        /* Extra large container */
    --container-2xl: 1536px;       /* 2X large container */
    --container-max: 800px;        /* Max width for hero content */

    /* ===== GRID SYSTEM ===== */
    --grid-columns: 12;            /* Standard 12-column grid */
    --grid-gap: var(--base-3);     /* 24px gap between grid items */
    --grid-gap-sm: var(--base-2);  /* 16px gap for smaller screens */
    --grid-gap-lg: var(--base-4);  /* 32px gap for larger screens */

    /* ===== COMPONENT MEASUREMENTS ===== */
    --nav-height: var(--base-8);   /* 64px navigation height */
    --button-height: 44px;         /* Minimum touch target (accessibility) */
    --input-height: 44px;          /* Consistent input height */
    --card-padding: var(--base-3); /* 24px standard card padding */
    --section-padding: var(--base-8); /* 64px section padding */
    --hero-max-width: var(--container-max); /* Hero content max width */
}

/* ===== GLOBAL RESET & BASE STYLES ===== */
* {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
}

html {
    font-size: 16px;
    scroll-behavior: smooth;
}

body {
    font-family: 'Montserrat', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    font-size: var(--font-size-base);
    line-height: 1.6;
    color: var(--text-primary);
    background-color: var(--onyx-black);
    overflow-x: hidden;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

/* ===== TYPOGRAPHY SYSTEM ===== */
h1, h2, h3, h4, h5, h6 {
    font-family: 'Orbitron', monospace;
    font-weight: 700;
    line-height: 1.2;
    margin-bottom: var(--space-4);
    color: var(--text-primary);
}

h1 { font-size: var(--font-size-5xl); }
h2 { font-size: var(--font-size-4xl); }
h3 { font-size: var(--font-size-3xl); }
h4 { font-size: var(--font-size-2xl); }
h5 { font-size: var(--font-size-xl); }
h6 { font-size: var(--font-size-lg); }

p {
    margin-bottom: var(--space-4);
    color: var(--text-secondary);
}

a {
    color: var(--color-primary);
    text-decoration: none;
    transition: color var(--transition-fast);
}

a:hover {
    color: var(--cyber-cyan-bright);
}

/* ===== UTILITY CLASSES ===== */
/* Background Colors */
.bg-onyx-black { background-color: var(--onyx-black); }
.bg-onyx-gray { background-color: var(--onyx-gray); }
.bg-onyx-light { background-color: var(--onyx-light); }
.bg-cyber-cyan { background-color: var(--cyber-cyan); }
.bg-cyber-purple { background-color: var(--cyber-purple); }
.bg-cyber-blue { background-color: var(--cyber-blue); }
.bg-cyber-green { background-color: var(--cyber-green); }

/* Text Colors */
.text-primary { color: var(--text-primary); }
.text-secondary { color: var(--text-secondary); }
.text-tertiary { color: var(--text-tertiary); }
.text-muted { color: var(--text-muted); }
.text-cyber-cyan { color: var(--cyber-cyan); }
.text-cyber-purple { color: var(--cyber-purple); }
.text-cyber-blue { color: var(--cyber-blue); }
.text-cyber-green { color: var(--cyber-green); }

/* ===== CORE ANIMATION SYSTEM ===== */
/* Essential animations for micro-interactions and cyberpunk effects */

/* Entrance Animations */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(var(--space-3)) scale(0.95);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

@keyframes slideIn {
    from {
        transform: translateX(-100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

@keyframes slideUp {
    from {
        transform: translateY(var(--space-6));
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

/* Glow Effects */
@keyframes cyberGlow {
    0%, 100% {
        box-shadow: 0 0 var(--space-3) var(--cyber-cyan-glow);
    }
    50% {
        box-shadow: 0 0 var(--space-6) var(--cyber-cyan-glow);
    }
}

@keyframes purpleGlow {
    0%, 100% {
        box-shadow: 0 0 var(--space-3) var(--cyber-purple-glow);
    }
    50% {
        box-shadow: 0 0 var(--space-6) var(--cyber-purple-glow);
    }
}

@keyframes blueGlow {
    0%, 100% {
        box-shadow: 0 0 var(--space-3) var(--cyber-blue-glow);
    }
    50% {
        box-shadow: 0 0 var(--space-6) var(--cyber-blue-glow);
    }
}

/* Background Animations */
@keyframes gradientShift {
    0% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
    100% { background-position: 0% 50%; }
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
}

/* Utility Animation Classes */
.fade-in { animation: fadeIn 0.8s ease-out; }
.slide-in { animation: slideIn 0.5s ease-out; }
.slide-up { animation: slideUp 0.6s ease-out; }
.glow-cyan { animation: cyberGlow 2s ease-in-out infinite; }
.glow-purple { animation: purpleGlow 2s ease-in-out infinite; }
.glow-blue { animation: blueGlow 2s ease-in-out infinite; }

/* ===== MODULAR GLASS MORPHISM COMPONENTS ===== */
/* Base glass card - foundation for all glass components */
.glass-card {
    background: var(--glass-bg);
    border: 1px solid var(--glass-border);
    border-radius: var(--radius-xl);
    backdrop-filter: blur(var(--glass-blur));
    -webkit-backdrop-filter: blur(var(--glass-blur));
    padding: var(--card-padding);
    transition: all var(--transition-normal);
    box-shadow: var(--shadow-lg);
    position: relative;
    overflow: hidden;
}

.glass-card:hover {
    background: var(--glass-hover);
    border-color: var(--glass-border-heavy);
    box-shadow: var(--shadow-xl);
    transform: translateY(-2px);
}

/* Glass card variants */
.glass-card-light {
    background: var(--glass-bg-light);
    border-color: var(--glass-border-light);
    backdrop-filter: blur(var(--glass-blur-light));
    -webkit-backdrop-filter: blur(var(--glass-blur-light));
}

.glass-card-heavy {
    background: var(--glass-bg-heavy);
    border-color: var(--glass-border-heavy);
    backdrop-filter: blur(var(--glass-blur-heavy));
    -webkit-backdrop-filter: blur(var(--glass-blur-heavy));
    padding: var(--space-6);
}

/* Premium glass card with enhanced effects */
.glass-card-premium {
    background: var(--glass-bg-medium);
    border: 2px solid var(--glass-border-heavy);
    border-radius: var(--radius-2xl);
    backdrop-filter: blur(var(--glass-blur-heavy)) saturate(1.2);
    -webkit-backdrop-filter: blur(var(--glass-blur-heavy)) saturate(1.2);
    padding: var(--space-6);
    box-shadow:
        var(--shadow-xl),
        0 0 0 1px rgba(255, 255, 255, 0.05),
        inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

.glass-card-premium:hover {
    border-color: var(--color-primary);
    box-shadow:
        var(--shadow-2xl),
        var(--shadow-glow-cyan),
        0 0 0 1px rgba(255, 255, 255, 0.1),
        inset 0 1px 0 rgba(255, 255, 255, 0.15);
    transform: translateY(-4px);
}

/* ===== MODULAR BUTTON SYSTEM ===== */
/* Base button styles with consistent touch targets */
.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: var(--space-2);
    min-height: var(--button-height);
    padding: var(--space-3) var(--space-6);
    border: none;
    border-radius: var(--radius-xl);
    font-family: 'Orbitron', monospace;
    font-weight: 600;
    font-size: var(--font-size-base);
    text-decoration: none;
    cursor: pointer;
    transition: all var(--transition-normal);
    position: relative;
    overflow: hidden;
}

/* Glass button - primary style */
.btn-glass {
    background: var(--glass-bg);
    border: 1px solid var(--glass-border);
    color: var(--text-primary);
    backdrop-filter: blur(var(--glass-blur));
    -webkit-backdrop-filter: blur(var(--glass-blur));
    box-shadow: var(--shadow-md);
}

.btn-glass:hover {
    background: var(--glass-hover);
    border-color: var(--glass-border-heavy);
    box-shadow: var(--shadow-lg);
    transform: translateY(-1px);
}

/* Primary button with cyber accent */
.btn-primary {
    background: linear-gradient(135deg, var(--cyber-cyan), var(--cyber-blue));
    color: var(--onyx-black);
    border: 1px solid var(--cyber-cyan);
    box-shadow: var(--shadow-md), var(--shadow-glow-cyan);
}

.btn-primary:hover {
    background: linear-gradient(135deg, var(--cyber-cyan-bright), var(--cyber-blue-bright));
    box-shadow: var(--shadow-lg), var(--shadow-glow-cyan);
    transform: translateY(-2px);
}

/* Secondary button with purple accent */
.btn-secondary {
    background: linear-gradient(135deg, var(--cyber-purple), var(--cyber-blue));
    color: var(--text-primary);
    border: 1px solid var(--cyber-purple);
    box-shadow: var(--shadow-md), var(--shadow-glow-purple);
}

.btn-secondary:hover {
    background: linear-gradient(135deg, var(--cyber-purple-bright), var(--cyber-blue-bright));
    box-shadow: var(--shadow-lg), var(--shadow-glow-purple);
    transform: translateY(-2px);
}

/* Outline button */
.btn-outline {
    background: transparent;
    border: 2px solid var(--cyber-cyan);
    color: var(--cyber-cyan);
}

.btn-outline:hover {
    background: var(--cyber-cyan);
    color: var(--onyx-black);
    box-shadow: var(--shadow-glow-cyan);
}

/* Button sizes */
.btn-sm {
    min-height: 36px;
    padding: var(--space-2) var(--space-4);
    font-size: var(--font-size-sm);
}

.btn-lg {
    min-height: 52px;
    padding: var(--space-4) var(--space-8);
    font-size: var(--font-size-lg);
}

/* Button states */
.btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none !important;
}

.btn:focus {
    outline: 2px solid var(--cyber-cyan);
    outline-offset: 2px;
}

/* ===== RESPONSIVE GRID SYSTEM ===== */
/* Consistent grid layouts with equal height cards */
.grid {
    display: grid;
    gap: var(--grid-gap);
    align-items: start;
    grid-auto-rows: auto;
}

.grid-1 { grid-template-columns: 1fr; }
.grid-2 { grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); }
.grid-3 { grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); }
.grid-4 { grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); }

/* Force equal heights when needed */
.grid-equal-height {
    align-items: stretch;
}

.grid-equal-height > * {
    height: 100%;
    display: flex;
    flex-direction: column;
}

/* Responsive grid adjustments */
@media (min-width: 768px) {
    .grid-2 { grid-template-columns: repeat(2, 1fr); }
    .grid-3 { grid-template-columns: repeat(3, 1fr); }
    .grid-4 { grid-template-columns: repeat(4, 1fr); }
}

@media (max-width: 640px) {
    .grid {
        gap: var(--grid-gap-sm);
    }
    .grid-2,
    .grid-3,
    .grid-4 {
        grid-template-columns: 1fr;
    }
}

/* ===== LAYOUT UTILITIES ===== */
/* Container system with consistent max-widths */
.container {
    width: 100%;
    margin: 0 auto;
    padding: 0 var(--space-4);
}

.container-xs { max-width: var(--container-xs); }
.container-sm { max-width: var(--container-sm); }
.container-md { max-width: var(--container-md); }
.container-lg { max-width: var(--container-lg); }
.container-xl { max-width: var(--container-xl); }
.container-2xl { max-width: var(--container-2xl); }

/* Hero content container with max-width constraint */
.hero-container {
    max-width: var(--hero-max-width);
    margin: 0 auto;
    text-align: center;
}

/* Section spacing */
.section {
    padding: var(--section-padding) 0;
}

.section-sm {
    padding: var(--space-12) 0;
}

.section-lg {
    padding: var(--space-20) 0;
}

/* Flexbox utilities */
.flex {
    display: flex;
}

.flex-col {
    flex-direction: column;
}

.flex-center {
    align-items: center;
    justify-content: center;
}

.flex-between {
    justify-content: space-between;
}

.flex-wrap {
    flex-wrap: wrap;
}

.flex-1 {
    flex: 1;
}

/* Gap utilities */
.gap-1 { gap: var(--space-1); }
.gap-2 { gap: var(--space-2); }
.gap-3 { gap: var(--space-3); }
.gap-4 { gap: var(--space-4); }
.gap-6 { gap: var(--space-6); }
.gap-8 { gap: var(--space-8); }

/* ===== ONNYX SPECIALIZED COMPONENTS ===== */
/* Tribal-themed components with consistent styling */

/* Statistics Card */
.stat-card {
    background: linear-gradient(135deg,
        rgba(26, 26, 26, 0.9),
        rgba(42, 42, 42, 0.9)
    );
    border: 2px solid var(--glass-border);
    border-radius: var(--radius-2xl);
    padding: var(--space-6);
    text-align: center;
    transition: all var(--transition-normal);
    position: relative;
    overflow: hidden;
}

.stat-card::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg,
        transparent,
        var(--color-primary),
        transparent
    );
    border-radius: 0 0 var(--radius-2xl) var(--radius-2xl);
}

.stat-card:hover {
    border-color: var(--color-primary);
    box-shadow: var(--shadow-glow-cyan);
    transform: translateY(-2px);
}

.stat-value {
    font-family: 'Orbitron', monospace;
    font-size: var(--font-size-4xl);
    font-weight: 700;
    color: var(--color-primary);
    margin-bottom: var(--space-2);
    display: block;
}

.stat-label {
    font-size: var(--font-size-sm);
    color: var(--text-tertiary);
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

/* Holographic Text Effect */
.hologram-text {
    background: linear-gradient(45deg,
        var(--cyber-cyan),
        var(--cyber-purple),
        var(--cyber-blue)
    );
    background-size: 200% 200%;
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
    animation: gradientShift 3s ease infinite;
}

/* Tribal Spinning N's */
.spinning-n {
    display: inline-block;
    transition: transform var(--transition-normal);
    cursor: pointer;
    position: relative;
}

.spinning-n:hover {
    transform: rotateY(360deg);
    color: var(--cyber-cyan-bright);
}

/* Tooltip System */
.tooltip {
    position: absolute;
    background: var(--glass-bg-heavy);
    border: 1px solid var(--glass-border-heavy);
    border-radius: var(--radius-lg);
    padding: var(--space-4);
    backdrop-filter: blur(var(--glass-blur));
    -webkit-backdrop-filter: blur(var(--glass-blur));
    box-shadow: var(--shadow-xl);
    z-index: 1000;
    opacity: 0;
    visibility: hidden;
    transition: all var(--transition-fast);
    pointer-events: none;
}

.tooltip.show {
    opacity: 1;
    visibility: visible;
}

.tooltip-header {
    font-family: 'Orbitron', monospace;
    font-weight: 600;
    color: var(--color-primary);
    margin-bottom: var(--space-2);
    font-size: var(--font-size-sm);
}

.tooltip-content {
    color: var(--text-secondary);
    font-size: var(--font-size-sm);
    line-height: 1.4;
}

/* Navigation Enhancement */
.nav-glass {
    background: rgba(26, 26, 26, 0.95);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border-bottom: 1px solid var(--glass-border);
    transition: all var(--transition-normal);
}

/* Hero Section Optimization */
.hero-section {
    min-height: 80vh;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    overflow: hidden;
}

.hero-content {
    max-width: var(--hero-max-width);
    margin: 0 auto;
    text-align: center;
    z-index: 10;
    position: relative;
}

/* ===== RESPONSIVE DESIGN SYSTEM ===== */
/* Mobile-first responsive breakpoints */

/* Small devices (landscape phones, 576px and up) */
@media (max-width: 640px) {
    .hero-section {
        min-height: 60vh;
        padding: var(--space-8) 0;
    }

    .hero-content h1 {
        font-size: var(--font-size-4xl);
    }

    .container {
        padding: 0 var(--space-3);
    }

    .btn {
        min-height: 48px;
        padding: var(--space-3) var(--space-4);
        font-size: var(--font-size-sm);
    }

    .glass-card {
        padding: var(--space-4);
        margin: var(--space-2);
        border-radius: var(--radius-lg);
    }

    .stat-card {
        padding: var(--space-4);
    }

    .stat-value {
        font-size: var(--font-size-3xl);
    }
}

/* Medium devices (tablets, 768px and up) */
@media (min-width: 768px) {
    .hero-content h1 {
        font-size: var(--font-size-6xl);
    }

    .grid {
        gap: var(--grid-gap-lg);
    }
}

/* Large devices (desktops, 1024px and up) */
@media (min-width: 1024px) {
    .container {
        padding: 0 var(--space-6);
    }

    .section {
        padding: var(--space-20) 0;
    }
}

/* ===== ACCESSIBILITY & PERFORMANCE ===== */
/* Focus states for keyboard navigation */
*:focus {
    outline: 2px solid var(--color-primary);
    outline-offset: 2px;
}

/* Reduced motion preferences */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }

    .spinning-n:hover {
        transform: none;
    }

    .hologram-text {
        animation: none;
        background: var(--color-primary);
        -webkit-background-clip: text;
        background-clip: text;
    }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    .glass-card {
        border-width: 2px;
        border-color: var(--text-primary);
    }

    .btn {
        border-width: 2px;
    }
}

/* Print styles */
@media print {
    .glass-card {
        background: white !important;
        border: 1px solid black !important;
        color: black !important;
        box-shadow: none !important;
    }

    .btn {
        background: white !important;
        border: 1px solid black !important;
        color: black !important;
    }
}

/* ===== UTILITY SPACING CLASSES ===== */
/* Margin utilities */
.m-0 { margin: 0; }
.m-1 { margin: var(--space-1); }
.m-2 { margin: var(--space-2); }
.m-3 { margin: var(--space-3); }
.m-4 { margin: var(--space-4); }
.m-6 { margin: var(--space-6); }
.m-8 { margin: var(--space-8); }

.mt-0 { margin-top: 0; }
.mt-1 { margin-top: var(--space-1); }
.mt-2 { margin-top: var(--space-2); }
.mt-3 { margin-top: var(--space-3); }
.mt-4 { margin-top: var(--space-4); }
.mt-6 { margin-top: var(--space-6); }
.mt-8 { margin-top: var(--space-8); }

.mb-0 { margin-bottom: 0; }
.mb-1 { margin-bottom: var(--space-1); }
.mb-2 { margin-bottom: var(--space-2); }
.mb-3 { margin-bottom: var(--space-3); }
.mb-4 { margin-bottom: var(--space-4); }
.mb-6 { margin-bottom: var(--space-6); }
.mb-8 { margin-bottom: var(--space-8); }

/* Padding utilities */
.p-0 { padding: 0; }
.p-1 { padding: var(--space-1); }
.p-2 { padding: var(--space-2); }
.p-3 { padding: var(--space-3); }
.p-4 { padding: var(--space-4); }
.p-6 { padding: var(--space-6); }
.p-8 { padding: var(--space-8); }

/* ===== END OF ONNYX PRECISION DESIGN SYSTEM V3.0 ===== */
