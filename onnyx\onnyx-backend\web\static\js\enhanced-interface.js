/**
 * ONNYX Platform - Enhanced Interface System
 * Advanced cyberpunk interactions, logo effects, and micro-animations
 */

class ONNYXEnhancedInterface {
    constructor() {
        this.logoContainer = null;
        this.logoImage = null;
        this.logoFallback = null;
        this.isReducedMotion = window.matchMedia('(prefers-reduced-motion: reduce)').matches;
        this.intersectionObserver = null;

        this.init();
    }

    init() {
        console.log('🚀 ONNYX Enhanced Interface Initializing...');

        this.initLogoSystem();
        this.initAdvancedAnimations();
        this.initGlassMorphismEffects();
        this.initMicroInteractions();
        this.initPerformanceOptimizations();

        console.log('✅ ONNYX Enhanced Interface Ready');
    }

    // ===== LOGO SYSTEM =====
    initLogoSystem() {
        this.logoContainer = document.getElementById('logo-icon-container');
        this.logoImage = document.getElementById('logo-image');
        this.logoFallback = document.getElementById('logo-fallback');
        const logoLink = document.getElementById('main-logo-link');

        if (!this.logoContainer || !logoLink) return;

        // Enhanced logo loading with fallback
        this.setupLogoFallback();

        // Advanced logo interactions
        this.setupLogoInteractions(logoLink);

        // Logo loading states
        this.setupLogoLoadingStates();
    }

    setupLogoFallback() {
        if (this.logoImage) {
            this.logoImage.addEventListener('load', () => {
                console.log('✅ Logo loaded successfully');
                this.logoImage.style.display = 'block';
                if (this.logoFallback) {
                    this.logoFallback.style.display = 'none';
                }
            });

            this.logoImage.addEventListener('error', () => {
                console.log('⚠️ Logo failed to load, using fallback');
                this.logoImage.style.display = 'none';
                if (this.logoFallback) {
                    this.logoFallback.style.display = 'block';
                }
            });
        }
    }

    setupLogoInteractions(logoLink) {
        if (!logoLink || this.isReducedMotion) return;

        // Simplified hover effects - no excessive glow
        logoLink.addEventListener('mouseenter', () => {
            // Simple hover state - no loading animation
        });

        logoLink.addEventListener('mouseleave', () => {
            // Simple hover state removal
        });

        // Simplified click effects
        logoLink.addEventListener('click', (e) => {
            // No special click effects - keep it professional
        });
    }

    setupLogoLoadingStates() {
        // No loading states - keep it simple and professional
        // Logo should be immediately visible without animations
    }

    // Removed complex glow and click effects for cleaner, professional appearance

    // ===== ADVANCED ANIMATIONS =====
    initAdvancedAnimations() {
        if (this.isReducedMotion) return;

        this.setupScrollAnimations();
        this.setupStaggeredAnimations();
        this.setupParallaxEffects();
    }

    setupScrollAnimations() {
        this.intersectionObserver = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.classList.add('animate-in');

                    // Stagger child animations
                    const children = entry.target.querySelectorAll('.stagger-child');
                    children.forEach((child, index) => {
                        setTimeout(() => {
                            child.classList.add('animate-in');
                        }, index * 100);
                    });
                }
            });
        }, {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        });

        // Observe elements for scroll animations
        document.querySelectorAll('.glass-card, .glass-card-enhanced, .glass-card-premium').forEach(el => {
            el.classList.add('animate-on-scroll');
            this.intersectionObserver.observe(el);
        });
    }

    setupStaggeredAnimations() {
        const staggerContainers = document.querySelectorAll('.stagger-container');

        staggerContainers.forEach(container => {
            const children = container.children;
            Array.from(children).forEach((child, index) => {
                child.classList.add('stagger-child');
                child.style.animationDelay = `${index * 0.1}s`;
            });
        });
    }

    setupParallaxEffects() {
        const parallaxElements = document.querySelectorAll('.parallax-element');

        if (parallaxElements.length === 0) return;

        const handleScroll = () => {
            const scrolled = window.pageYOffset;

            parallaxElements.forEach(element => {
                const rate = scrolled * -0.5;
                element.style.transform = `translateY(${rate}px)`;
            });
        };

        window.addEventListener('scroll', handleScroll, { passive: true });
    }

    // ===== GLASS MORPHISM EFFECTS =====
    initGlassMorphismEffects() {
        this.setupDynamicBlur();
        this.setupGlassInteractions();
    }

    setupDynamicBlur() {
        const glassElements = document.querySelectorAll('.glass-card, .glass-card-enhanced, .glass-card-premium');

        glassElements.forEach(element => {
            element.addEventListener('mouseenter', () => {
                if (!this.isReducedMotion) {
                    element.style.backdropFilter = 'blur(32px) saturate(1.4) brightness(1.2)';
                    element.style.webkitBackdropFilter = 'blur(32px) saturate(1.4) brightness(1.2)';
                }
            });

            element.addEventListener('mouseleave', () => {
                element.style.backdropFilter = '';
                element.style.webkitBackdropFilter = '';
            });
        });
    }

    setupGlassInteractions() {
        const glassElements = document.querySelectorAll('.glass-card, .glass-card-enhanced, .glass-card-premium');

        glassElements.forEach(element => {
            element.addEventListener('mousemove', (e) => {
                if (this.isReducedMotion) return;

                const rect = element.getBoundingClientRect();
                const x = e.clientX - rect.left;
                const y = e.clientY - rect.top;

                const centerX = rect.width / 2;
                const centerY = rect.height / 2;

                // Much more subtle movement - professional level
                const rotateX = (y - centerY) / 100;        /* Reduced from /20 to /100 */
                const rotateY = (centerX - x) / 100;        /* Reduced from /20 to /100 */

                // Very subtle 3D effect
                element.style.transform = `perspective(1000px) rotateX(${rotateX}deg) rotateY(${rotateY}deg) translateZ(1px)`;
            });

            element.addEventListener('mouseleave', () => {
                element.style.transform = 'perspective(1000px) rotateX(0deg) rotateY(0deg) translateZ(0px)';
                element.style.transition = 'transform 0.3s ease';
            });
        });
    }

    // ===== MICRO INTERACTIONS =====
    initMicroInteractions() {
        this.setupButtonEffects();
        this.setupHoverStates();
        this.setupFocusStates();
    }

    setupButtonEffects() {
        const buttons = document.querySelectorAll('.glass-button, .glass-button-primary, .btn');

        buttons.forEach(button => {
            button.addEventListener('click', (e) => {
                if (this.isReducedMotion) return;

                const ripple = document.createElement('span');
                const rect = button.getBoundingClientRect();
                const size = Math.max(rect.width, rect.height);
                const x = e.clientX - rect.left - size / 2;
                const y = e.clientY - rect.top - size / 2;

                ripple.style.cssText = `
                    position: absolute;
                    width: ${size}px;
                    height: ${size}px;
                    left: ${x}px;
                    top: ${y}px;
                    background: radial-gradient(circle, rgba(0, 255, 247, 0.3) 0%, transparent 70%);
                    border-radius: 50%;
                    transform: scale(0);
                    animation: buttonRipple 0.6s ease-out;
                    pointer-events: none;
                `;

                button.style.position = 'relative';
                button.style.overflow = 'hidden';
                button.appendChild(ripple);

                setTimeout(() => {
                    if (ripple.parentNode) {
                        ripple.parentNode.removeChild(ripple);
                    }
                }, 600);
            });
        });
    }

    setupHoverStates() {
        const hoverElements = document.querySelectorAll('[data-hover-effect]');

        hoverElements.forEach(element => {
            element.addEventListener('mouseenter', () => {
                if (!this.isReducedMotion) {
                    element.classList.add('hover-active');
                }
            });

            element.addEventListener('mouseleave', () => {
                element.classList.remove('hover-active');
            });
        });
    }

    setupFocusStates() {
        const focusableElements = document.querySelectorAll('button, a, input, select, textarea');

        focusableElements.forEach(element => {
            element.addEventListener('focus', () => {
                element.classList.add('focus-visible');
            });

            element.addEventListener('blur', () => {
                element.classList.remove('focus-visible');
            });
        });
    }

    // ===== PERFORMANCE OPTIMIZATIONS =====
    initPerformanceOptimizations() {
        this.setupLazyLoading();
        this.setupDebouncing();
    }

    setupLazyLoading() {
        const lazyElements = document.querySelectorAll('[data-lazy]');

        if (lazyElements.length === 0) return;

        const lazyObserver = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    const element = entry.target;
                    const src = element.dataset.lazy;

                    if (src) {
                        element.src = src;
                        element.removeAttribute('data-lazy');
                        lazyObserver.unobserve(element);
                    }
                }
            });
        });

        lazyElements.forEach(element => {
            lazyObserver.observe(element);
        });
    }

    setupDebouncing() {
        // Debounce scroll events
        let scrollTimeout;
        const originalScrollHandler = window.onscroll;

        window.onscroll = (e) => {
            clearTimeout(scrollTimeout);
            scrollTimeout = setTimeout(() => {
                if (originalScrollHandler) {
                    originalScrollHandler(e);
                }
            }, 16); // ~60fps
        };
    }

    // ===== CLEANUP =====
    destroy() {
        if (this.intersectionObserver) {
            this.intersectionObserver.disconnect();
        }
    }
}

// Initialize when DOM is ready
document.addEventListener('DOMContentLoaded', () => {
    window.onnyx_enhanced_interface = new ONNYXEnhancedInterface();
});

// Add required CSS animations
const style = document.createElement('style');
style.textContent = `
    @keyframes logoGlowPulse {
        0% { opacity: 0; transform: scale(0.8); }
        50% { opacity: 1; transform: scale(1.1); }
        100% { opacity: 0; transform: scale(1.3); }
    }

    @keyframes logoRipple {
        0% { width: 0; height: 0; opacity: 1; }
        100% { width: 200px; height: 200px; opacity: 0; }
    }

    @keyframes buttonRipple {
        0% { transform: scale(0); opacity: 1; }
        100% { transform: scale(1); opacity: 0; }
    }

    .animate-on-scroll {
        opacity: 0;
        transform: translateY(30px);
        transition: all 0.6s ease-out;
    }

    .animate-on-scroll.animate-in {
        opacity: 1;
        transform: translateY(0);
    }

    .stagger-child {
        opacity: 0;
        transform: translateY(20px);
        transition: all 0.4s ease-out;
    }

    .stagger-child.animate-in {
        opacity: 1;
        transform: translateY(0);
    }

    .focus-visible {
        outline: 2px solid var(--cyber-cyan);
        outline-offset: 2px;
    }

    .hover-active {
        transform: translateY(-2px);
        box-shadow: 0 0 20px rgba(0, 255, 247, 0.3);
    }
`;
document.head.appendChild(style);
