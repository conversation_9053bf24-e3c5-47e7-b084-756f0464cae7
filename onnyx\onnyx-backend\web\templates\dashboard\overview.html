{% extends "base.html" %}

{% block title %}Dashboard Overview - ONNYX Platform{% endblock %}

{% block content %}
<section class="hero-section hero-gradient cyber-grid">
    <!-- Subtle floating particles -->
    <div class="absolute inset-0 overflow-hidden pointer-events-none">
        <div class="absolute top-1/4 left-1/4 w-2 h-2 bg-cyber-cyan rounded-full glow-cyan"></div>
        <div class="absolute top-1/3 right-1/3 w-1 h-1 bg-cyber-purple rounded-full glow-purple"></div>
        <div class="absolute bottom-1/4 left-1/3 w-1.5 h-1.5 bg-cyber-blue rounded-full glow-blue"></div>
    </div>

    <div class="container container-lg">
        <!-- Header Section -->
        <div class="hero-content fade-in">
            <!-- ONNYX Logo -->
            <div class="mb-8 flex justify-center">
                <img src="{{ url_for('static', filename='images/onnyx_logo.png') }}"
                     alt="ONNYX Logo"
                     class="onnyx-hero-logo w-20 h-20 object-contain"
                     onerror="this.style.display='none'; this.nextElementSibling.style.display='block';">
                <!-- Fallback symbol -->
                <span class="text-5xl font-black text-cyber-cyan" style="display: none;">⬢</span>
            </div>

            <h1 class="text-4xl md:text-5xl font-orbitron font-bold mb-6">
                <span class="hologram-text">Command Center</span>
            </h1>
            <p class="text-xl text-secondary max-w-4xl mx-auto leading-relaxed">
                Welcome back, {{ current_user.name }}. Your ONNYX network control dashboard.
            </p>
        </div>

        <!-- Statistics Grid -->
        <div class="grid grid-4 grid-equal-height mb-16 slide-up">
            <div class="stat-card">
                <div class="stat-value text-cyber-cyan">{{ stats.active_selas }}</div>
                <div class="stat-label">Active Validators</div>
            </div>
            <div class="stat-card">
                <div class="stat-value text-cyber-purple">{{ stats.total_blocks_mined }}</div>
                <div class="stat-label">Blocks Mined</div>
            </div>
            <div class="stat-card">
                <div class="stat-value text-cyber-blue">{{ stats.total_mining_power }}x</div>
                <div class="stat-label">Mining Power</div>
            </div>
            <div class="stat-card">
                <div class="stat-value text-cyber-green">{{ stats.total_mining_rewards|round(2) }}</div>
                <div class="stat-label">Mining Rewards</div>
            </div>
        </div>
    </div>
</section>

<!-- Dashboard Content -->
<section class="section-spacing">
    <div class="container container-lg">
        <!-- Mining Tier Status -->
        {% if user_selas %}
        <div class="glass-card mb-16 slide-up">
            <div class="flex items-center justify-between">
                <div class="flex items-center gap-4">
                    <div class="w-12 h-12 bg-gradient-to-br from-cyber-cyan to-cyber-purple rounded-xl flex flex-center">
                        <svg class="w-6 h-6 text-onyx-black" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                        </svg>
                    </div>
                    <div>
                        <h3 class="text-xl font-orbitron font-bold text-primary">Mining Tier: {{ stats.highest_mining_tier|title }}</h3>
                        <p class="text-secondary">
                            {% if stats.highest_mining_tier == 'basic' %}
                                Standard CPU mining with 1x reward multiplier
                            {% elif stats.highest_mining_tier == 'optimized' %}
                                ONNYX Optimized miner with 2x-5x reward multiplier
                            {% elif stats.highest_mining_tier == 'pro' %}
                                ONNYX Pro validator with 5x+ reward multiplier
                            {% endif %}
                        </p>
                    </div>
                </div>
                <div class="text-right">
                    <div class="text-2xl font-orbitron font-bold text-cyber-cyan">{{ stats.total_mining_power }}x</div>
                    <div class="text-sm text-tertiary">Total Power</div>
                </div>
            </div>
        </div>
        {% endif %}

        <!-- Biblical Tokenomics Overview -->
        {% if biblical_data %}
        <div class="glass-card-heavy mb-16 slide-up">
            <div class="flex items-center justify-between mb-8">
                <h2 class="text-2xl font-orbitron font-bold text-cyber-cyan">📜 Biblical Tokenomics Status</h2>
                <a href="{{ url_for('tokenomics.overview') }}" class="btn btn-secondary">
                    View Details →
                </a>
            </div>

            <div class="grid grid-4 grid-equal-height">
                <!-- Yovel Cycle Status -->
                <div class="glass-card text-center">
                    <div class="w-12 h-12 mx-auto mb-4 bg-gradient-to-br from-cyber-cyan to-cyber-blue rounded-xl flex flex-center">
                        <svg class="w-6 h-6 text-onyx-black" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                    </div>
                    <div class="text-2xl font-orbitron font-bold text-cyber-cyan mb-2">Cycle {{ biblical_data.current_yovel_cycle }}</div>
                    <div class="text-sm text-text-secondary mb-3">Yovel ({{ biblical_data.yovel_cycle_years }} Year)</div>
                    <div class="w-full bg-onyx-light rounded-full h-2 mb-2">
                        <div class="bg-gradient-to-r from-cyber-cyan to-cyber-blue h-2 rounded-full transition-all duration-500"
                             style="width: {{ biblical_data.yovel_progress }}%"></div>
                    </div>
                    <div class="text-xs text-text-tertiary">{{ biblical_data.years_until_reset }} years until reset</div>
                </div>

                <!-- Sabbath Status -->
                <div class="glass-card-enhanced p-6 text-center stagger-child" data-hover-effect>
                    <div class="w-12 h-12 mx-auto mb-4 bg-gradient-to-br from-cyber-purple to-cyber-blue rounded-xl flex items-center justify-center">
                        {% if biblical_data.is_sabbath_period %}
                        <svg class="w-6 h-6 text-onyx-black" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z"></path>
                        </svg>
                        {% else %}
                        <svg class="w-6 h-6 text-onyx-black" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z"></path>
                        </svg>
                        {% endif %}
                    </div>
                    <div class="text-lg font-orbitron font-bold mb-2 {{ 'text-cyber-purple' if biblical_data.is_sabbath_period else 'text-cyber-blue' }}">
                        {{ 'Sabbath Active' if biblical_data.is_sabbath_period else 'Sabbath Rest' }}
                    </div>
                    <div class="text-sm text-text-secondary mb-2">
                        {{ 'Mining Restricted' if biblical_data.is_sabbath_period else 'Next: ' + biblical_data.next_sabbath }}
                    </div>
                    <div class="text-xs text-text-tertiary">
                        {{ biblical_data.sabbath_bonus_multiplier }}x bonus for observers
                    </div>
                </div>

                <!-- Gleaning Pool -->
                <div class="glass-card-enhanced p-6 text-center stagger-child" data-hover-effect>
                    <div class="w-12 h-12 mx-auto mb-4 bg-gradient-to-br from-cyber-green to-cyber-blue rounded-xl flex items-center justify-center">
                        <svg class="w-6 h-6 text-onyx-black" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 9V7a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2m2 4h10a2 2 0 002-2v-6a2 2 0 00-2-2H9a2 2 0 00-2 2v6a2 2 0 002 2zm7-5a2 2 0 11-4 0 2 2 0 014 0z"></path>
                        </svg>
                    </div>
                    <div class="text-2xl font-orbitron font-bold text-cyber-green mb-2">{{ biblical_data.gleaning_pool.current_balance|round(2) }}</div>
                    <div class="text-sm text-text-secondary mb-2">Gleaning Pool (ONX)</div>
                    <div class="text-xs text-text-tertiary">{{ biblical_data.gleaning_pool.contributor_count }} contributors</div>
                    <a href="{{ url_for('tokenomics.gleaning') }}"
                       class="inline-block mt-2 text-xs text-cyber-green hover:text-white transition-colors">
                        View Pool →
                    </a>
                </div>

                <!-- User Yovel Status -->
                <div class="glass-card-enhanced p-6 text-center stagger-child" data-hover-effect>
                    <div class="w-12 h-12 mx-auto mb-4 bg-gradient-to-br from-cyber-yellow to-cyber-orange rounded-xl flex items-center justify-center">
                        <svg class="w-6 h-6 text-onyx-black" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                        </svg>
                    </div>
                    <div class="text-2xl font-orbitron font-bold text-cyber-yellow mb-2">{{ biblical_data.user_yovel_tokens|round(2) }}</div>
                    <div class="text-sm text-text-secondary mb-2">Your Yovel Tokens</div>
                    <div class="text-xs {{ 'text-cyber-green' if biblical_data.user_yovel_eligible else 'text-cyber-red' }}">
                        {{ 'Eligible for more' if biblical_data.user_yovel_eligible else 'Cycle limit reached' }}
                    </div>
                    <div class="text-xs text-text-tertiary mt-1">
                        Max: {{ biblical_data.max_tokens_per_yovel }} per cycle
                    </div>
                </div>
            </div>
        </div>
        {% endif %}

        <!-- Enhanced Mining Control Panel -->
        <div class="glass-card-premium p-8 mb-16">
            <div class="flex items-center justify-between mb-8">
                <h2 class="text-3xl font-orbitron font-bold text-cyber-purple">⛏️ Mining Control Center</h2>
                <div class="flex items-center space-x-2">
                    <div class="w-3 h-3 rounded-full {{ 'bg-cyber-green animate-pulse' if mining_data.is_mining else 'bg-cyber-red' }}"></div>
                    <span class="text-sm font-orbitron {{ 'text-cyber-green' if mining_data.is_mining else 'text-cyber-red' }}">
                        {{ 'MINING ACTIVE' if mining_data.is_mining else 'MINING INACTIVE' }}
                    </span>
                </div>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
                <!-- Mining Status -->
                <div class="glass-card-enhanced p-6 text-center">
                    <div class="w-12 h-12 mx-auto mb-4 bg-gradient-to-br from-cyber-purple to-cyber-blue rounded-xl flex items-center justify-center">
                        <svg class="w-6 h-6 text-onyx-black" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                        </svg>
                    </div>
                    <div class="text-lg font-orbitron font-bold text-cyber-purple mb-2">
                        {{ 'Active' if mining_data.is_mining else 'Inactive' }}
                    </div>
                    <div class="text-sm text-text-secondary">Mining Status</div>
                </div>

                <!-- Mining Power -->
                <div class="glass-card-enhanced p-6 text-center">
                    <div class="w-12 h-12 mx-auto mb-4 bg-gradient-to-br from-cyber-cyan to-cyber-green rounded-xl flex items-center justify-center">
                        <svg class="w-6 h-6 text-onyx-black" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"></path>
                        </svg>
                    </div>
                    <div class="text-2xl font-orbitron font-bold text-cyber-cyan mb-2">{{ stats.total_mining_power }}x</div>
                    <div class="text-sm text-text-secondary">Total Power</div>
                </div>

                <!-- Estimated Rewards -->
                <div class="glass-card-enhanced p-6 text-center">
                    <div class="w-12 h-12 mx-auto mb-4 bg-gradient-to-br from-cyber-green to-cyber-yellow rounded-xl flex items-center justify-center">
                        <svg class="w-6 h-6 text-onyx-black" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
                        </svg>
                    </div>
                    <div class="text-2xl font-orbitron font-bold text-cyber-green mb-2">{{ mining_data.estimated_rewards|round(2) }}</div>
                    <div class="text-sm text-text-secondary">Est. Rewards/Block</div>
                </div>

                <!-- Sabbath Compliance -->
                <div class="glass-card-enhanced p-6 text-center">
                    <div class="w-12 h-12 mx-auto mb-4 bg-gradient-to-br from-cyber-blue to-cyber-purple rounded-xl flex items-center justify-center">
                        {% if mining_data.can_mine_sabbath %}
                        <svg class="w-6 h-6 text-onyx-black" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                        {% else %}
                        <svg class="w-6 h-6 text-onyx-black" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M18.364 18.364A9 9 0 005.636 5.636m12.728 12.728L5.636 5.636m12.728 12.728L18.364 5.636M5.636 18.364l12.728-12.728"></path>
                        </svg>
                        {% endif %}
                    </div>
                    <div class="text-lg font-orbitron font-bold mb-2 {{ 'text-cyber-green' if mining_data.can_mine_sabbath else 'text-cyber-red' }}">
                        {{ 'Compliant' if mining_data.can_mine_sabbath else 'Restricted' }}
                    </div>
                    <div class="text-sm text-text-secondary">Sabbath Status</div>
                </div>
            </div>

            <!-- Timezone & Sabbath Configuration -->
            <div class="glass-card-enhanced p-6 mb-6">
                <h3 class="text-lg font-orbitron font-bold text-cyber-cyan mb-4">🌍 Timezone & Sabbath Settings</h3>
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div>
                        <label class="block text-sm text-text-secondary mb-2">Your Timezone</label>
                        <select id="userTimezone"
                                class="form-input w-full"
                                onchange="updateUserTimezone()">
                            <option value="America/Chicago" {{ 'selected' if session.get('user_timezone', 'America/Chicago') == 'America/Chicago' else '' }}>Central Time (CST/CDT)</option>
                            <option value="America/New_York" {{ 'selected' if session.get('user_timezone') == 'America/New_York' else '' }}>Eastern Time (EST/EDT)</option>
                            <option value="America/Denver" {{ 'selected' if session.get('user_timezone') == 'America/Denver' else '' }}>Mountain Time (MST/MDT)</option>
                            <option value="America/Los_Angeles" {{ 'selected' if session.get('user_timezone') == 'America/Los_Angeles' else '' }}>Pacific Time (PST/PDT)</option>
                            <option value="Asia/Jerusalem" {{ 'selected' if session.get('user_timezone') == 'Asia/Jerusalem' else '' }}>Jerusalem Time (IST)</option>
                            <option value="Europe/London" {{ 'selected' if session.get('user_timezone') == 'Europe/London' else '' }}>London Time (GMT/BST)</option>
                            <option value="UTC" {{ 'selected' if session.get('user_timezone') == 'UTC' else '' }}>UTC</option>
                        </select>
                    </div>
                    <div>
                        <label class="block text-sm text-text-secondary mb-2">Current Time</label>
                        <div id="currentTime" class="p-3 glass-card rounded-lg text-cyber-cyan font-mono text-sm">
                            Loading...
                        </div>
                    </div>
                    <div>
                        <label class="block text-sm text-text-secondary mb-2">Sabbath Status</label>
                        <div id="sabbathStatus" class="p-3 glass-card rounded-lg text-sm">
                            <span class="flex items-center space-x-2">
                                <div class="w-2 h-2 rounded-full {{ 'bg-cyber-red' if biblical_data.is_sabbath_period else 'bg-cyber-green' }}"></div>
                                <span class="{{ 'text-cyber-red' if biblical_data.is_sabbath_period else 'text-cyber-green' }}">
                                    {{ 'Sabbath Active' if biblical_data.is_sabbath_period else 'Mining Allowed' }}
                                </span>
                            </span>
                        </div>
                    </div>
                </div>
                <div class="mt-4 text-xs text-text-tertiary">
                    <p>⚠️ Sabbath observance: Friday 6:00 PM to Saturday 7:00 PM in your timezone</p>
                    <p>📍 Current timezone: <span id="currentTimezoneDisplay">{{ session.get('user_timezone', 'America/Chicago') }}</span></p>
                </div>
            </div>

            <!-- Mining Controls -->
            <div class="flex flex-col sm:flex-row gap-4 justify-center items-center">
                {% if mining_data.can_mine_sabbath %}
                <button id="toggleMining"
                        class="glass-button-primary px-8 py-4 rounded-2xl font-orbitron font-bold text-lg transition-all duration-300 hover:scale-105"
                        onclick="toggleMining()">
                    {{ 'Stop Mining' if mining_data.is_mining else 'Start Mining' }}
                </button>
                {% else %}
                <button class="glass-button px-8 py-4 rounded-2xl font-orbitron font-bold text-lg opacity-50 cursor-not-allowed"
                        disabled>
                    Mining Restricted (Sabbath)
                </button>
                {% endif %}

                <button onclick="mineBlock()"
                        class="glass-button-enhanced px-8 py-4 rounded-2xl font-orbitron font-bold text-lg transition-all duration-300 hover:scale-105"
                        {{ 'disabled' if not mining_data.can_mine_sabbath else '' }}>
                    ⛏️ Mine Single Block
                </button>

                <div class="flex items-center space-x-4">
                    <label class="text-sm text-text-secondary">Intensity:</label>
                    <select id="miningIntensity"
                            class="glass-card px-3 py-2 rounded-lg text-sm bg-transparent border border-glass-border text-white"
                            onchange="updateMiningIntensity()">
                        <option value="low" {{ 'selected' if mining_data.mining_intensity == 'low' else '' }}>Low</option>
                        <option value="medium" {{ 'selected' if mining_data.mining_intensity == 'medium' else '' }}>Medium</option>
                        <option value="high" {{ 'selected' if mining_data.mining_intensity == 'high' else '' }}>High</option>
                        <option value="maximum" {{ 'selected' if mining_data.mining_intensity == 'maximum' else '' }}>Maximum</option>
                    </select>
                </div>
            </div>
        </div>

        <!-- Main Dashboard Grid -->
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
            <!-- Identity Information -->
            <div class="lg:col-span-2 space-y-8">
                <!-- Identity Card -->
                <div class="glass-card-enhanced p-8">
                    <h2 class="text-2xl font-orbitron font-bold text-cyber-cyan mb-6">🔐 Identity Profile</h2>

                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label class="block text-sm text-gray-500 mb-1">Identity ID</label>
                            <div class="p-3 glass-card rounded-lg font-mono text-sm text-cyber-cyan break-all">
                                {{ current_user.identity_id }}
                            </div>
                        </div>

                        <div>
                            <label class="block text-sm text-gray-500 mb-1">Name</label>
                            <div class="p-3 glass-card rounded-lg text-white">
                                {{ current_user.name }}
                            </div>
                        </div>

                        <div>
                            <label class="block text-sm text-gray-500 mb-1">Email</label>
                            <div class="p-3 glass-card rounded-lg text-white">
                                {{ current_user.email }}
                            </div>
                        </div>

                        <div>
                            <label class="block text-sm text-gray-500 mb-1">Status</label>
                            <div class="p-3 glass-card rounded-lg">
                                <span class="badge-success">{{ current_user.status|title }}</span>
                            </div>
                        </div>

                        <div>
                            <label class="block text-sm text-gray-500 mb-1">Role</label>
                            <div class="p-3 glass-card rounded-lg text-white">
                                {{ current_user.role or 'Not specified' }}
                            </div>
                        </div>

                        <div>
                            <label class="block text-sm text-gray-500 mb-1">Registered</label>
                            <div class="p-3 glass-card rounded-lg text-white">
                                {{ current_user.created_at|timestamp_to_date if current_user.created_at else 'Unknown' }}
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Recent Transactions -->
                <div class="glass-card p-8 neuro-card">
                    <div class="flex items-center justify-between mb-6">
                        <h2 class="text-2xl font-orbitron font-bold text-cyber-purple">💫 Recent Activity</h2>
                        <a href="{{ url_for('dashboard.transactions') }}" class="text-cyber-purple hover:text-white transition-colors text-sm">
                            View All →
                        </a>
                    </div>

                    {% if user_transactions %}
                        <div class="space-y-4">
                            {% for tx in user_transactions[:5] %}
                            <div class="glass-card p-4 hover:bg-white/10 transition-all duration-300 data-stream">
                                <div class="flex items-center justify-between">
                                    <div>
                                        <div class="flex items-center space-x-3 mb-2">
                                            <span class="text-sm font-orbitron font-bold text-white">{{ tx.op or 'Transaction' }}</span>
                                            <span class="badge-success">{{ tx.status|title }}</span>
                                        </div>
                                        <div class="text-sm text-gray-400">
                                            TX: <span class="font-mono text-cyber-purple hash-truncate">{{ tx.tx_id }}</span>
                                        </div>
                                    </div>
                                    <div class="text-right">
                                        <div class="text-sm text-gray-500">{{ tx.created_at|timestamp_to_time if tx.created_at else 'Unknown' }}</div>
                                        <div class="text-xs text-gray-600">{{ tx.created_at|timestamp_to_date if tx.created_at else '' }}</div>
                                    </div>
                                </div>
                            </div>
                            {% endfor %}
                        </div>
                    {% else %}
                        <div class="text-center py-8">
                            <div class="w-16 h-16 bg-gradient-to-br from-cyber-purple to-cyber-blue rounded-xl flex items-center justify-center mx-auto mb-4">
                                <svg class="w-8 h-8 text-onyx-black" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16V4m0 0L3 8m4-4l4 4m6 0v12m0 0l4-4m-4 4l-4-4"></path>
                                </svg>
                            </div>
                            <p class="text-gray-500">No transactions yet</p>
                        </div>
                    {% endif %}
                </div>
            </div>

            <!-- Sidebar -->
            <div class="space-y-8">
                <!-- Quick Actions -->
                <div class="glass-card-enhanced p-8">
                    <h2 class="text-2xl font-orbitron font-bold text-cyber-blue mb-6">🚀 Quick Actions</h2>

                    <div class="space-y-4">
                        <button onclick="showSelaCreationPopup()"
                                class="w-full glass-button-primary px-6 py-3 rounded-xl font-orbitron font-semibold text-center transition-all duration-300 hover:scale-105">
                            🏢 Register Validator
                        </button>

                        <a href="{{ url_for('explorer.index') }}"
                           class="w-full glass-button-enhanced px-6 py-3 rounded-xl font-orbitron font-semibold text-center block transition-all duration-300 hover:scale-105">
                            🔍 Explore Network
                        </a>

                        <a href="{{ url_for('sela.directory') }}"
                           class="w-full glass-button px-6 py-3 rounded-xl font-orbitron font-semibold text-center block transition-all duration-300 hover:scale-105">
                           🌐 Validator Directory
                        </a>
                    </div>
                </div>

                <!-- Biblical Tokenomics Quick Access -->
                <div class="glass-card-enhanced p-8">
                    <h2 class="text-2xl font-orbitron font-bold text-cyber-cyan mb-6">📜 Biblical Economics</h2>

                    <div class="space-y-4">
                        <a href="{{ url_for('tokenomics.overview') }}"
                           class="w-full glass-button-primary px-6 py-3 rounded-xl font-orbitron font-semibold text-center block transition-all duration-300 hover:scale-105">
                            📊 Tokenomics Overview
                        </a>

                        <a href="{{ url_for('tokenomics.gleaning') }}"
                           class="w-full glass-button px-6 py-3 rounded-xl font-orbitron font-semibold text-center block transition-all duration-300 hover:scale-105">
                            🌾 Gleaning Pool
                        </a>

                        <a href="{{ url_for('tokenomics.loans') }}"
                           class="w-full glass-button px-6 py-3 rounded-xl font-orbitron font-semibold text-center block transition-all duration-300 hover:scale-105">
                            💰 Anti-Usury Loans
                        </a>

                        <a href="{{ url_for('tokenomics.firstfruits') }}"
                           class="w-full glass-button px-6 py-3 rounded-xl font-orbitron font-semibold text-center block transition-all duration-300 hover:scale-105">
                            🍇 Firstfruits Offering
                        </a>

                        <a href="{{ url_for('tokenomics.calculator') }}"
                           class="w-full glass-button px-6 py-3 rounded-xl font-orbitron font-semibold text-center block transition-all duration-300 hover:scale-105">
                            🧮 Covenant Calculator
                        </a>
                    </div>

                    <!-- Sabbath Status Indicator -->
                    {% if biblical_data %}
                    <div class="mt-6 p-4 glass-card rounded-lg">
                        <div class="flex items-center justify-between">
                            <span class="text-sm text-text-secondary">Sabbath Status:</span>
                            <div class="flex items-center space-x-2">
                                <div class="w-2 h-2 rounded-full {{ 'bg-cyber-purple animate-pulse' if biblical_data.is_sabbath_period else 'bg-cyber-green' }}"></div>
                                <span class="text-xs font-orbitron {{ 'text-cyber-purple' if biblical_data.is_sabbath_period else 'text-cyber-green' }}">
                                    {{ 'ACTIVE' if biblical_data.is_sabbath_period else 'INACTIVE' }}
                                </span>
                            </div>
                        </div>
                        {% if not biblical_data.is_sabbath_period %}
                        <div class="text-xs text-text-tertiary mt-2">
                            Next: {{ biblical_data.next_sabbath }}
                        </div>
                        {% endif %}
                    </div>
                    {% endif %}
                </div>

                <!-- My Validators -->
                <div class="glass-card p-8 neuro-card">
                    <h2 class="text-2xl font-orbitron font-bold text-green-400 mb-6">🏢 My Validators</h2>

                    {% if user_selas %}
                        <div class="space-y-4">
                            {% for sela in user_selas %}
                            <div class="glass-card p-4 hover:bg-white/10 transition-all duration-300">
                                <div class="flex items-center justify-between mb-2">
                                    <h3 class="font-orbitron font-bold text-white">{{ sela.name }}</h3>
                                    <div class="flex items-center space-x-2">
                                        {% if sela.mining_tier == 'pro' %}
                                        <span class="badge-success bg-gradient-to-r from-cyber-cyan to-cyber-purple">PRO {{ sela.mining_power }}x</span>
                                        {% elif sela.mining_tier == 'optimized' %}
                                        <span class="badge-success bg-gradient-to-r from-cyber-blue to-cyber-cyan">OPT {{ sela.mining_power }}x</span>
                                        {% else %}
                                        <span class="badge-success">BASIC {{ sela.mining_power }}x</span>
                                        {% endif %}
                                        <span class="badge-success">{{ sela.status|title }}</span>
                                    </div>
                                </div>
                                <div class="text-sm text-gray-400 space-y-1">
                                    <div>Category: {{ sela.category }}</div>
                                    <div>ID: <span class="font-mono text-cyber-cyan hash-truncate">{{ sela.sela_id }}</span></div>
                                    <div class="flex items-center justify-between">
                                        <span>Rewards: {{ sela.mining_rewards_earned|round(2) }}</span>
                                        <span>Blocks: {{ sela.blocks_mined }}</span>
                                    </div>
                                </div>
                            </div>
                            {% endfor %}
                        </div>
                    {% else %}
                        <div class="text-center py-6">
                            <div class="w-12 h-12 bg-gradient-to-br from-green-400 to-cyber-blue rounded-xl flex items-center justify-center mx-auto mb-3">
                                <svg class="w-6 h-6 text-onyx-black" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-4m-5 0H9m0 0H5m0 0h2M7 7h10M7 11h10M7 15h10"></path>
                                </svg>
                            </div>
                            <p class="text-gray-500 text-sm mb-3">No validators registered</p>
                            <button onclick="showSelaCreationPopup()"
                                    class="text-cyber-cyan hover:text-white text-sm transition-colors cursor-pointer">
                                Register your first validator →
                            </button>
                        </div>
                    {% endif %}
                </div>

                <!-- Network Status -->
                <div class="glass-card p-8 neuro-card">
                    <h2 class="text-2xl font-orbitron font-bold text-yellow-400 mb-6">📡 Network Status</h2>

                    <div class="space-y-4">
                        <div class="flex items-center justify-between">
                            <span class="text-gray-400">Network</span>
                            <div class="flex items-center space-x-2">
                                <div class="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
                                <span class="text-green-400 font-orbitron">ONLINE</span>
                            </div>
                        </div>

                        <div class="flex items-center justify-between">
                            <span class="text-gray-400">Latest Block</span>
                            <span class="text-cyber-cyan font-mono">{{ latest_block or 'N/A' }}</span>
                        </div>

                        <div class="flex items-center justify-between">
                            <span class="text-gray-400">Active Validators</span>
                            <span class="text-cyber-purple font-mono">{{ total_validators or 0 }}</span>
                        </div>

                        <div class="flex items-center justify-between">
                            <span class="text-gray-400">Network Hashrate</span>
                            <span class="text-cyber-blue font-mono">{{ network_hashrate or 'Auto' }}</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Sela Creation Popup -->
<div id="selaCreationPopup" class="fixed inset-0 bg-black/80 backdrop-blur-sm z-50 hidden">
    <div class="flex items-center justify-center min-h-screen p-4">
        <div class="glass-card-enhanced max-w-2xl w-full max-h-[90vh] overflow-y-auto">
            <!-- Popup Header -->
            <div class="flex items-center justify-between p-6 border-b border-glass-border">
                <h2 class="text-2xl font-orbitron font-bold text-cyber-cyan">
                    🏢 Create Your Sela Business
                </h2>
                <button onclick="hideSelaCreationPopup()"
                        class="text-gray-400 hover:text-white transition-colors">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                </button>
            </div>

            <!-- Popup Content -->
            <div class="p-6">
                <div class="mb-6">
                    <p class="text-text-secondary leading-relaxed">
                        Register your business as a covenant validator on the ONNYX network.
                        Earn mining rewards while upholding biblical economic principles.
                    </p>
                </div>

                <!-- Sela Creation Form -->
                <form id="selaCreationForm" class="space-y-6">
                    <!-- Business Name -->
                    <div class="form-group">
                        <label class="form-label">Business Name *</label>
                        <input type="text"
                               id="sela_name"
                               name="sela_name"
                               class="form-input"
                               placeholder="Enter your business name"
                               required>
                        <div id="nameValidation" class="text-sm mt-1 hidden"></div>
                    </div>

                    <!-- Category -->
                    <div class="form-group">
                        <label class="form-label">Business Category *</label>
                        <select id="category" name="category" class="form-input" required>
                            <option value="">Select a category</option>
                            <option value="Technology">Technology & Software</option>
                            <option value="Finance">Financial Services</option>
                            <option value="Healthcare">Healthcare & Wellness</option>
                            <option value="Education">Education & Training</option>
                            <option value="Retail">Retail & E-commerce</option>
                            <option value="Manufacturing">Manufacturing</option>
                            <option value="Agriculture">Agriculture & Food</option>
                            <option value="Construction">Construction & Real Estate</option>
                            <option value="Transportation">Transportation & Logistics</option>
                            <option value="Energy">Energy & Utilities</option>
                            <option value="Media">Media & Entertainment</option>
                            <option value="Consulting">Consulting & Professional Services</option>
                            <option value="Other">Other</option>
                        </select>
                    </div>

                    <!-- Description -->
                    <div class="form-group">
                        <label class="form-label">Business Description *</label>
                        <textarea id="description"
                                  name="description"
                                  class="form-input"
                                  rows="4"
                                  placeholder="Describe your business, services, and covenant commitment"
                                  required></textarea>
                    </div>

                    <!-- Address (Optional) -->
                    <div class="form-group">
                        <label class="form-label">Business Address</label>
                        <input type="text"
                               id="address"
                               name="address"
                               class="form-input"
                               placeholder="Enter your business address (optional)">
                    </div>

                    <!-- Covenant Agreement -->
                    <div class="form-group">
                        <div class="glass-card p-4 border border-cyber-cyan/30">
                            <h4 class="font-orbitron font-semibold text-cyber-cyan mb-3">Covenant Agreement</h4>
                            <div class="space-y-2 text-sm text-text-secondary">
                                <p>✓ Uphold biblical economic principles</p>
                                <p>✓ Participate in Sabbath observance</p>
                                <p>✓ Contribute to gleaning pools for community support</p>
                                <p>✓ Practice anti-usury lending policies</p>
                                <p>✓ Honor Yovel (Jubilee) cycles and debt forgiveness</p>
                            </div>
                            <label class="flex items-center mt-4">
                                <input type="checkbox" id="acceptTerms" name="acceptTerms" class="mr-3" required>
                                <span class="text-sm">I agree to uphold these covenant principles</span>
                            </label>
                        </div>
                    </div>

                    <!-- Submit Button -->
                    <div class="flex gap-4 pt-4">
                        <button type="button"
                                onclick="hideSelaCreationPopup()"
                                class="glass-button px-6 py-3 rounded-xl font-orbitron font-semibold flex-1">
                            Cancel
                        </button>
                        <button type="submit"
                                id="submitSelaBtn"
                                class="glass-button-primary px-6 py-3 rounded-xl font-orbitron font-semibold flex-1 transition-all duration-300">
                            Create Sela Business
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
// Enhanced Dashboard JavaScript with Biblical Tokenomics and Mining Controls

// Global state
let miningState = {
    isActive: {{ 'true' if mining_data.is_mining else 'false' }},
    intensity: '{{ mining_data.mining_intensity }}',
    canMineSabbath: {{ 'true' if mining_data.can_mine_sabbath else 'false' }},
    intervalId: null
};

// Initialize dashboard when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
    initializeDashboard();
    updateMiningStatus();
    updateCurrentTime();

    // Auto-refresh biblical data every 5 minutes
    setInterval(refreshBiblicalData, 300000);

    // Auto-refresh mining status every 30 seconds
    setInterval(updateMiningStatus, 30000);

    // Update current time every second
    setInterval(updateCurrentTime, 1000);
});

function initializeDashboard() {
    console.log('🚀 ONNYX Dashboard Initialized');

    // Initialize enhanced interface if available
    if (window.onnyx_enhanced_interface) {
        console.log('✅ Enhanced interface active');
    }

    // Setup mining controls
    setupMiningControls();

    // Setup biblical tokenomics interactions
    setupBiblicalTokenomicsInteractions();
}

// ===== MINING CONTROL FUNCTIONS =====

function setupMiningControls() {
    const toggleButton = document.getElementById('toggleMining');
    const intensitySelect = document.getElementById('miningIntensity');

    if (toggleButton) {
        toggleButton.addEventListener('click', toggleMining);
    }

    if (intensitySelect) {
        intensitySelect.addEventListener('change', updateMiningIntensity);
    }
}

async function toggleMining() {
    try {
        const button = document.getElementById('toggleMining');
        const originalText = button.textContent;

        // Check Sabbath compliance
        if (!miningState.canMineSabbath && !miningState.isActive) {
            showNotification('Mining is restricted during Sabbath period', 'warning');
            return;
        }

        button.disabled = true;
        button.textContent = miningState.isActive ? 'Stopping...' : 'Starting...';

        const action = miningState.isActive ? 'stop' : 'start';
        const response = await fetch('/api/mining/toggle', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                action: action,
                intensity: miningState.intensity
            })
        });

        const result = await response.json();

        if (result.success) {
            miningState.isActive = !miningState.isActive;
            updateMiningUI();
            showNotification(
                `Mining ${miningState.isActive ? 'started' : 'stopped'} successfully`,
                'success'
            );
        } else {
            showNotification(result.error || 'Mining toggle failed', 'error');
        }

    } catch (error) {
        console.error('Mining toggle error:', error);
        showNotification('Mining toggle failed', 'error');
    } finally {
        const button = document.getElementById('toggleMining');
        button.disabled = false;
        updateMiningButtonText();
    }
}

async function updateMiningIntensity() {
    try {
        const select = document.getElementById('miningIntensity');
        const newIntensity = select.value;

        const response = await fetch('/api/mining/intensity', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                intensity: newIntensity
            })
        });

        const result = await response.json();

        if (result.success) {
            miningState.intensity = newIntensity;
            showNotification(`Mining intensity set to ${newIntensity}`, 'success');
        } else {
            showNotification(result.error || 'Failed to update mining intensity', 'error');
            // Revert selection
            select.value = miningState.intensity;
        }

    } catch (error) {
        console.error('Mining intensity update error:', error);
        showNotification('Failed to update mining intensity', 'error');
    }
}

async function mineBlock() {
    try {
        const button = event.target;
        const originalText = button.textContent;

        // Check Sabbath compliance
        if (!miningState.canMineSabbath) {
            showNotification('Block mining is restricted during Sabbath period', 'warning');
            return;
        }

        button.textContent = '⛏️ Mining...';
        button.disabled = true;

        const response = await fetch('/api/mine-block', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            }
        });

        const result = await response.json();

        if (result.success) {
            showNotification(`Block mined! Height: ${result.block_height}`, 'success');

            // Update stats
            setTimeout(updateMiningStatus, 1000);
        } else {
            showNotification(result.error || 'Mining failed', 'error');
        }
    } catch (error) {
        console.error('Mining error:', error);
        showNotification('Mining failed', 'error');
    } finally {
        button.textContent = originalText;
        button.disabled = false;
    }
}

function updateMiningUI() {
    const statusIndicator = document.querySelector('.mining-status-indicator');
    const statusText = document.querySelector('.mining-status-text');

    if (statusIndicator) {
        statusIndicator.className = `w-3 h-3 rounded-full ${
            miningState.isActive ? 'bg-cyber-green animate-pulse' : 'bg-cyber-red'
        }`;
    }

    if (statusText) {
        statusText.textContent = miningState.isActive ? 'MINING ACTIVE' : 'MINING INACTIVE';
        statusText.className = `text-sm font-orbitron ${
            miningState.isActive ? 'text-cyber-green' : 'text-cyber-red'
        }`;
    }

    updateMiningButtonText();
}

function updateMiningButtonText() {
    const button = document.getElementById('toggleMining');
    if (button && !button.disabled) {
        button.textContent = miningState.isActive ? 'Stop Mining' : 'Start Mining';
    }
}

async function updateMiningStatus() {
    try {
        const response = await fetch('/api/mining/status');
        const data = await response.json();

        if (data.success) {
            miningState.isActive = data.is_mining;
            miningState.canMineSabbath = data.can_mine_sabbath;
            updateMiningUI();
        }
    } catch (error) {
        console.error('Error updating mining status:', error);
    }
}

// ===== BIBLICAL TOKENOMICS FUNCTIONS =====

function setupBiblicalTokenomicsInteractions() {
    // Add click handlers for biblical tokenomics cards
    const yovelCard = document.querySelector('[data-yovel-card]');
    const sabbathCard = document.querySelector('[data-sabbath-card]');
    const gleaningCard = document.querySelector('[data-gleaning-card]');

    if (yovelCard) {
        yovelCard.addEventListener('click', () => {
            window.location.href = '/tokenomics/overview#yovel';
        });
    }

    if (sabbathCard) {
        sabbathCard.addEventListener('click', () => {
            window.location.href = '/tokenomics/overview#sabbath';
        });
    }

    if (gleaningCard) {
        gleaningCard.addEventListener('click', () => {
            window.location.href = '/tokenomics/gleaning';
        });
    }
}

async function refreshBiblicalData() {
    try {
        const response = await fetch('/api/biblical-tokenomics/status');
        const data = await response.json();

        if (data.success) {
            // Update Sabbath status
            miningState.canMineSabbath = !data.is_sabbath_period;

            // Update UI elements
            updateSabbathStatus(data);
            updateYovelProgress(data);
            updateGleaningPool(data);
        }
    } catch (error) {
        console.error('Error refreshing biblical data:', error);
    }
}

function updateSabbathStatus(data) {
    const sabbathIndicators = document.querySelectorAll('.sabbath-status-indicator');
    sabbathIndicators.forEach(indicator => {
        indicator.className = `w-2 h-2 rounded-full ${
            data.is_sabbath_period ? 'bg-cyber-purple animate-pulse' : 'bg-cyber-green'
        }`;
    });

    const sabbathTexts = document.querySelectorAll('.sabbath-status-text');
    sabbathTexts.forEach(text => {
        text.textContent = data.is_sabbath_period ? 'ACTIVE' : 'INACTIVE';
        text.className = `text-xs font-orbitron ${
            data.is_sabbath_period ? 'text-cyber-purple' : 'text-cyber-green'
        }`;
    });
}

function updateYovelProgress(data) {
    const progressBar = document.querySelector('.yovel-progress-bar');
    if (progressBar && data.yovel_progress !== undefined) {
        progressBar.style.width = `${data.yovel_progress}%`;
    }
}

function updateGleaningPool(data) {
    const balanceElement = document.querySelector('.gleaning-balance');
    if (balanceElement && data.gleaning_pool) {
        balanceElement.textContent = data.gleaning_pool.current_balance.toFixed(2);
    }
}

// ===== UTILITY FUNCTIONS =====

function showNotification(message, type = 'info') {
    // Use existing notification system if available
    if (window.Onnyx && window.Onnyx.utils && window.Onnyx.utils.showNotification) {
        window.Onnyx.utils.showNotification(message, type);
    } else {
        // Fallback notification
        console.log(`${type.toUpperCase()}: ${message}`);

        // Create simple notification
        const notification = document.createElement('div');
        notification.className = `fixed right-4 p-4 rounded-lg z-50 transition-all duration-300 ${
            type === 'success' ? 'bg-cyber-green text-onyx-black' :
            type === 'error' ? 'bg-cyber-red text-white' :
            type === 'warning' ? 'bg-cyber-yellow text-onyx-black' :
            'bg-cyber-blue text-white'
        }`;
        notification.style.top = 'calc(var(--nav-height) + 1rem)'; // Position below navigation bar
        notification.textContent = message;

        document.body.appendChild(notification);

        setTimeout(() => {
            notification.remove();
        }, 5000);
    }
}

// ===== KEYBOARD SHORTCUTS =====

document.addEventListener('keydown', function(e) {
    // Ctrl/Cmd + M: Toggle mining
    if ((e.ctrlKey || e.metaKey) && e.key === 'm') {
        e.preventDefault();
        if (miningState.canMineSabbath) {
            toggleMining();
        }
    }

    // Ctrl/Cmd + B: Mine single block
    if ((e.ctrlKey || e.metaKey) && e.key === 'b') {
        e.preventDefault();
        if (miningState.canMineSabbath) {
            const mineButton = document.querySelector('[onclick="mineBlock()"]');
            if (mineButton) {
                mineButton.click();
            }
        }
    }
});

// ===== REAL-TIME UPDATES =====

// WebSocket connection for real-time updates (if available)
function initializeWebSocket() {
    if (typeof WebSocket !== 'undefined') {
        try {
            const ws = new WebSocket(`ws://${window.location.host}/ws/dashboard`);

            ws.onmessage = function(event) {
                const data = JSON.parse(event.data);

                if (data.type === 'mining_status') {
                    miningState.isActive = data.is_mining;
                    updateMiningUI();
                }

                if (data.type === 'sabbath_status') {
                    miningState.canMineSabbath = !data.is_sabbath_period;
                    updateSabbathStatus(data);
                }

                if (data.type === 'block_mined') {
                    showNotification(`New block mined! Height: ${data.block_height}`, 'success');
                    updateMiningStatus();
                }
            };

            ws.onerror = function(error) {
                console.log('WebSocket connection failed, using polling instead');
            };

        } catch (error) {
            console.log('WebSocket not available, using polling instead');
        }
    }
}

// Initialize WebSocket if available
setTimeout(initializeWebSocket, 1000);

// ===== SELA CREATION POPUP FUNCTIONS =====

function showSelaCreationPopup() {
    const popup = document.getElementById('selaCreationPopup');
    if (popup) {
        popup.classList.remove('hidden');
        // Focus on first input
        setTimeout(() => {
            const firstInput = popup.querySelector('#sela_name');
            if (firstInput) firstInput.focus();
        }, 100);
    }
}

function hideSelaCreationPopup() {
    const popup = document.getElementById('selaCreationPopup');
    if (popup) {
        popup.classList.add('hidden');
        // Reset form
        const form = document.getElementById('selaCreationForm');
        if (form) form.reset();
        // Clear validation messages
        const validation = document.getElementById('nameValidation');
        if (validation) {
            validation.classList.add('hidden');
            validation.textContent = '';
        }
    }
}

// Handle form submission
document.addEventListener('DOMContentLoaded', function() {
    const form = document.getElementById('selaCreationForm');
    const nameInput = document.getElementById('sela_name');

    if (form) {
        form.addEventListener('submit', handleSelaCreation);
    }

    if (nameInput) {
        // Add debounced name validation
        let nameValidationTimeout;
        nameInput.addEventListener('input', function() {
            clearTimeout(nameValidationTimeout);
            nameValidationTimeout = setTimeout(validateSelaName, 500);
        });
    }

    // Check if popup should be shown on page load
    const urlParams = new URLSearchParams(window.location.search);
    if (urlParams.get('show_sela_popup') === 'true') {
        showSelaCreationPopup();
        // Clean up URL
        window.history.replaceState({}, document.title, window.location.pathname);
    }
});

async function validateSelaName() {
    const nameInput = document.getElementById('sela_name');
    const validation = document.getElementById('nameValidation');

    if (!nameInput || !validation) return;

    const name = nameInput.value.trim();
    if (!name) {
        validation.classList.add('hidden');
        return;
    }

    try {
        const response = await fetch('/api/validate/sela-name', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({ name: name })
        });

        const result = await response.json();

        validation.textContent = result.message;
        validation.className = `text-sm mt-1 ${result.valid ? 'text-cyber-green' : 'text-cyber-red'}`;
        validation.classList.remove('hidden');

    } catch (error) {
        console.error('Name validation error:', error);
    }
}

async function handleSelaCreation(event) {
    event.preventDefault();

    const submitBtn = document.getElementById('submitSelaBtn');
    const originalText = submitBtn.textContent;

    try {
        // Disable submit button
        submitBtn.disabled = true;
        submitBtn.textContent = 'Creating...';

        // Get form data
        const formData = new FormData(event.target);
        const data = {
            sela_name: formData.get('sela_name'),
            category: formData.get('category'),
            description: formData.get('description'),
            address: formData.get('address'),
            acceptTerms: formData.get('acceptTerms') === 'on'
        };

        // Validate required fields
        if (!data.sela_name || !data.category || !data.description || !data.acceptTerms) {
            showNotification('Please fill in all required fields and accept the covenant agreement', 'error');
            return;
        }

        // Submit to API
        const response = await fetch('/api/sela/create-dashboard', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(data)
        });

        const result = await response.json();

        if (result.success) {
            showNotification(result.message, 'success');
            hideSelaCreationPopup();

            // Refresh page to show new validator
            setTimeout(() => {
                window.location.reload();
            }, 1500);
        } else {
            showNotification(result.error || 'Sela creation failed', 'error');
        }

    } catch (error) {
        console.error('Sela creation error:', error);
        showNotification('Sela creation failed. Please try again.', 'error');
    } finally {
        // Re-enable submit button
        submitBtn.disabled = false;
        submitBtn.textContent = originalText;
    }
}

// Close popup when clicking outside
document.addEventListener('click', function(event) {
    const popup = document.getElementById('selaCreationPopup');
    if (popup && event.target === popup) {
        hideSelaCreationPopup();
    }
});

// Close popup with Escape key
document.addEventListener('keydown', function(event) {
    if (event.key === 'Escape') {
        hideSelaCreationPopup();
    }
});

// ===== TIMEZONE AND ENHANCED MINING FUNCTIONS =====

function updateCurrentTime() {
    const timezone = document.getElementById('userTimezone')?.value || 'America/Chicago';

    try {
        const now = new Date();
        const timeString = now.toLocaleString('en-US', {
            timeZone: timezone,
            weekday: 'short',
            year: 'numeric',
            month: 'short',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit',
            second: '2-digit',
            hour12: true
        });

        const timeElement = document.getElementById('currentTime');
        if (timeElement) {
            timeElement.textContent = timeString;
        }

        // Update Sabbath status based on current time
        updateSabbathStatusDisplay(now, timezone);

    } catch (error) {
        console.error('Error updating current time:', error);
    }
}

function updateSabbathStatusDisplay(now, timezone) {
    try {
        // Get current day and time in user's timezone
        const localTime = new Date(now.toLocaleString('en-US', { timeZone: timezone }));
        const dayOfWeek = localTime.getDay(); // 0=Sunday, 6=Saturday
        const hour = localTime.getHours();

        // Check if it's Sabbath (Friday 6 PM to Saturday 7 PM)
        let isSabbath = false;

        // Friday after 6 PM
        if (dayOfWeek === 5 && hour >= 18) {
            isSabbath = true;
        }
        // Saturday before 7 PM
        else if (dayOfWeek === 6 && hour < 19) {
            isSabbath = true;
        }

        const statusElement = document.getElementById('sabbathStatus');
        if (statusElement) {
            const indicator = statusElement.querySelector('.w-2');
            const text = statusElement.querySelector('span:last-child');

            if (indicator && text) {
                if (isSabbath) {
                    indicator.className = 'w-2 h-2 rounded-full bg-cyber-red animate-pulse';
                    text.className = 'text-cyber-red';
                    text.textContent = 'Sabbath Active';
                } else {
                    indicator.className = 'w-2 h-2 rounded-full bg-cyber-green';
                    text.className = 'text-cyber-green';
                    text.textContent = 'Mining Allowed';
                }
            }
        }

        // Update mining state
        miningState.canMineSabbath = !isSabbath;
        updateMiningUI();

    } catch (error) {
        console.error('Error updating Sabbath status:', error);
    }
}

async function updateUserTimezone() {
    const timezoneSelect = document.getElementById('userTimezone');
    const timezone = timezoneSelect.value;

    try {
        const response = await fetch('/api/user/timezone', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({ timezone: timezone })
        });

        const result = await response.json();

        if (result.success) {
            // Update display
            const displayElement = document.getElementById('currentTimezoneDisplay');
            if (displayElement) {
                displayElement.textContent = timezone;
            }

            // Immediately update time display
            updateCurrentTime();

            showNotification(`Timezone updated to ${timezone}`, 'success');
        } else {
            showNotification(result.error || 'Failed to update timezone', 'error');
        }

    } catch (error) {
        console.error('Error updating timezone:', error);
        showNotification('Failed to update timezone', 'error');
    }
}

// Enhanced mining functions with real functionality
async function mineBlock() {
    try {
        const button = event.target;
        const originalText = button.textContent;

        // Check Sabbath compliance
        if (!miningState.canMineSabbath) {
            showNotification('Block mining is restricted during Sabbath period', 'warning');
            return;
        }

        button.textContent = '⛏️ Mining...';
        button.disabled = true;

        const response = await fetch('/api/mine-block', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            }
        });

        const result = await response.json();

        if (result.success) {
            showNotification(
                `Block mined successfully! Height: ${result.block_height}, Reward: ${result.reward} ONX`,
                'success'
            );

            // Update stats
            setTimeout(updateMiningStatus, 1000);
            setTimeout(() => window.location.reload(), 2000); // Refresh to show updated stats
        } else {
            if (result.sabbath_active) {
                showNotification('Mining is not allowed during Sabbath period', 'warning');
            } else {
                showNotification(result.error || 'Mining failed', 'error');
            }
        }
    } catch (error) {
        console.error('Mining error:', error);
        showNotification('Mining failed', 'error');
    } finally {
        const button = event.target;
        button.textContent = originalText;
        button.disabled = false;
    }
}

// Enhanced toggle mining with real API integration
async function toggleMining() {
    try {
        const button = document.getElementById('toggleMining');
        const originalText = button.textContent;

        // Check Sabbath compliance
        if (!miningState.canMineSabbath && !miningState.isActive) {
            showNotification('Mining is restricted during Sabbath period', 'warning');
            return;
        }

        button.disabled = true;
        button.textContent = miningState.isActive ? 'Stopping...' : 'Starting...';

        const action = miningState.isActive ? 'stop' : 'start';
        const response = await fetch('/api/mining/toggle', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                action: action,
                intensity: miningState.intensity
            })
        });

        const result = await response.json();

        if (result.success) {
            miningState.isActive = !miningState.isActive;
            updateMiningUI();
            showNotification(
                `Mining ${miningState.isActive ? 'started' : 'stopped'} successfully`,
                'success'
            );
        } else {
            showNotification(result.error || 'Mining toggle failed', 'error');
        }

    } catch (error) {
        console.error('Mining toggle error:', error);
        showNotification('Mining toggle failed', 'error');
    } finally {
        const button = document.getElementById('toggleMining');
        button.disabled = false;
        updateMiningButtonText();
    }
}

// Enhanced mining intensity update
async function updateMiningIntensity() {
    try {
        const select = document.getElementById('miningIntensity');
        const newIntensity = select.value;

        const response = await fetch('/api/mining/intensity', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                intensity: newIntensity
            })
        });

        const result = await response.json();

        if (result.success) {
            miningState.intensity = newIntensity;
            showNotification(`Mining intensity set to ${newIntensity}`, 'success');
        } else {
            showNotification(result.error || 'Failed to update mining intensity', 'error');
            // Revert selection
            select.value = miningState.intensity;
        }

    } catch (error) {
        console.error('Mining intensity update error:', error);
        showNotification('Failed to update mining intensity', 'error');
    }
}

// Enhanced mining status update with timezone awareness
async function updateMiningStatus() {
    try {
        const response = await fetch('/api/mining/status');
        const data = await response.json();

        if (data.success) {
            miningState.isActive = data.is_mining;
            miningState.canMineSabbath = data.can_mine_sabbath;
            miningState.intensity = data.mining_intensity;
            updateMiningUI();
        }
    } catch (error) {
        console.error('Error updating mining status:', error);
    }
}
</script>
{% endblock %}
